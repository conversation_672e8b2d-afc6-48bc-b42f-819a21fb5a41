<!-- 配置化表单组件 -->
<template>
  <van-form
    ref="formRef"
    @submit="handleSubmit"
    @failed="handleFailed"
    required="auto"
  >
    <van-cell-group>
      <template v-for="field in formConfig" :key="field.prop">
        <!-- 输入框 -->
        <van-field
          v-if="!field.type"
          v-model="formData[field.prop]"
          :name="field.prop"
          :label="field.label"
          v-bind="field.props"
          :rules="field.rules"
          readonly
        />
        <!-- 输入框 -->
        <van-field
          v-if="field.type === 'input'"
          v-model="formData[field.prop]"
          :name="field.prop"
          placeholder="请输入"
          :label="field.label"
          v-bind="field.props"
          :rules="field.rules"
        />

        <!-- 文本域 -->
        <van-field
          v-else-if="field.type === 'textarea'"
          v-model="formData[field.prop]"
          :name="field.prop"
          :label="field.label"
          type="textarea"
          placeholder="请输入"
          v-bind="field.props"
          :rules="field.rules"
        />

        <!-- 选择器 -->
        <van-field
          v-else-if="field.type === 'select'"
          v-model="selectTexts[field.prop]"
          is-link
          readonly
          placeholder="请选择"
          :name="field.prop"
          :label="field.label"
          v-bind="field.props"
          :rules="field.rules"
          @click="openPicker(field)"
        />

        <!-- 日期选择器 -->
        <van-field
          v-else-if="field.type === 'date'"
          v-model="formData[field.prop]"
          is-link
          readonly
          :name="field.prop"
          :label="field.label"
          placeholder="请选择"
          v-bind="field.props"
          :rules="field.rules"
          @click="openDatePicker(field)"
        />

        <!-- 时间选择器 -->
        <DateTimePicker
          v-else-if="field.type === 'datetime'"
          v-model="formData[field.prop]"
          :field-props="{
            name: field.prop,
            label: field.label,
            placeholder: field.placeholder || '请选择',
            rules: field.rules,
          }"
          :title="'请选择' + field.label"
        />

        <!-- 数字输入框 -->
        <van-field
          v-else-if="field.type === 'number'"
          v-model="formData[field.prop]"
          :name="field.prop"
          :label="field.label"
          type="number"
          :rules="field.rules"
          placeholder="请输入"
          v-bind="field.props"
        >
          <template #button>
              <span v-if="field.suffix">{{field.suffix}}</span>
          </template>
        </van-field>

        <!-- 开关 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'switch'"
          :rules="field.rules"
        >
          <template #input>
            <van-switch v-model="formData[field.prop]" v-bind="field.props" />
          </template>
        </van-field>

        <!-- 单选框组 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'radio'"
          :rules="field.rules"
        >
          <template #input>
            <van-radio-group
              v-model="formData[field.prop]"
              v-bind="field.props"
            >
              <van-radio
                v-for="option in field.options"
                :key="option.value"
                :name="option.value"
              >
                {{ option.text }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <!-- 复选框组 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'checkbox'"
          :rules="field.rules"
        >
          <template #input>
            <van-checkbox-group
              v-model="formData[field.prop]"
              v-bind="field.props"
            >
              <van-checkbox
                v-for="option in field.options"
                :key="option.value"
                :name="option.value"
              >
                {{ option.text }}
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>

        <!-- 评分 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'rate'"
          :rules="field.rules"
        >
          <template #input>
            <van-rate v-model="formData[field.prop]" v-bind="field.props" />
          </template>
        </van-field>

        <!-- 滑块 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'slider'"
          :rules="field.rules"
        >
          <template #input>
            <van-slider v-model="formData[field.prop]" v-bind="field.props" />
          </template>
        </van-field>

        <!-- 步进器 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'stepper'"
          :rules="field.rules"
        >
          <template #input>
            <van-stepper v-model="formData[field.prop]" v-bind="field.props" />
          </template>
        </van-field>

        <!-- 上传 -->
        <van-field
          :name="field.prop"
          :label="field.label"
          v-else-if="field.type === 'upload'"
          :rules="field.rules"
        >
          <template #input>
            <van-uploader
              v-model="formData[field.prop]"
              v-bind="field.props"
              @oversize="handleOversize"
            />
          </template>
        </van-field>
      </template>
    </van-cell-group>
  </van-form>

  <!-- 选择器弹窗 -->
  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="currentPickerOptions"
      @confirm="onPickerConfirm"
      @cancel="showPicker = false"
    />
  </van-popup>

  <!-- 日期选择器弹窗 -->
  <van-popup v-model:show="showDatePicker" position="bottom">
    <van-date-picker
      v-model="currentDate"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
      :min-date="currentDateField?.props?.minDate"
      :max-date="currentDateField?.props?.maxDate"
      :type="currentDateField?.props?.dateType || 'date'"
    />
  </van-popup>

  <!-- 时间选择器弹窗 -->
  <van-popup v-model:show="showTimePicker" position="bottom">
    <van-time-picker
      v-model="currentTime"
      @confirm="onTimeConfirm"
      @cancel="showTimePicker = false"
    />
  </van-popup>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { showToast } from "vant";
import moment from "moment";
import "moment/locale/zh-cn"; // 导入中文本地化
import useExpert from "@/hook/expert.js";

// 设置 moment.js 为中文
moment.locale("zh-cn");

// Props
const props = defineProps({
  // 表单配置
  formConfig: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "submit", "failed"]);

// 响应式数据
const formRef = ref();
const formData = reactive({});
const selectTexts = reactive({});

// 选择器相关
const showPicker = ref(false);
const currentPickerField = ref(null);
const currentPickerOptions = ref([]);

// 日期选择器相关
const showDatePicker = ref(false);
const currentDateField = ref(null);
const currentDate = ref();

// 时间选择器相关
const showTimePicker = ref(false);
const currentTimeField = ref(null);
const currentTime = ref(["12", "00"]);

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach((key) => {
    delete formData[key];
  });
  Object.keys(selectTexts).forEach((key) => {
    delete selectTexts[key];
  });

  // 设置默认值
  props.formConfig.forEach((field) => {
    const defaultValue =
      field.props?.defaultValue !== undefined
        ? field.props.defaultValue
        : getFieldDefaultValue(field.type);
    formData[field.prop] =
      props.modelValue[field.prop] !== undefined
        ? props.modelValue[field.prop]
        : defaultValue;

    // 设置选择器显示文本
    if (field.type === "select" && field.options) {
      const option = field.options.find(
        (opt) => opt.value === formData[field.prop]
      );
      selectTexts[field.prop] = option ? option.text : "";
    }
  });
};

// 获取字段默认值
const getFieldDefaultValue = (type) => {
  const defaultValues = {
    input: "",
    textarea: "",
    select: "",
    date: "",
    time: "",
    number: "",
    switch: false,
    radio: "",
    checkbox: [],
    rate: 0,
    slider: 0,
    stepper: 0,
    upload: [],
  };
  return defaultValues[type] || "";
};

// 日期格式化工具方法
const formatDateByType = (date, dateType) => {
  const momentDate = moment(date);
  if (!momentDate.isValid()) {
    return "";
  }

  const formatMap = {
    date: "YYYY-MM-DD",
    datetime: "YYYY-MM-DD HH:mm:ss",
    "year-month": "YYYY-MM",
    year: "YYYY",
    "month-day": "MM-DD",
    time: "HH:mm:ss",
  };

  return momentDate.format(formatMap[dateType] || "YYYY-MM-DD");
};

// 监听外部数据变化
// watch(() => props.modelValue, (newValue) => {
//   Object.assign(formData,newValue)
// }, { immediate: true })

// 监听配置变化
watch(
  () => props.formConfig,
  () => {
    initFormData();
  },
  { immediate: true }
);

// 打开选择器
const openPicker = (field) => {
  if (field.disabled || field.readonly) return;

  currentPickerField.value = field;
  currentPickerOptions.value = field.options || [];
  showPicker.value = true;
};

// 选择器确认
const onPickerConfirm = ({ selectedOptions }) => {
  const field = currentPickerField.value;
  const option = selectedOptions[0];
  formData[field.prop] = option.value;
  selectTexts[field.prop] = option.text;
  showPicker.value = false;
};

// 打开日期选择器
const openDatePicker = (field) => {
  if (field.props?.disabled || field.props?.readonly) return;
  currentDateField.value = field;
  // 使用工具方法解析日期
  let dateMoment = moment(formData[field.prop] || undefined);
  currentDate.value = [
    dateMoment.format("YYYY"),
    dateMoment.format("MM"),
    dateMoment.format("DD"),
  ];
  showDatePicker.value = true;
};

// 日期选择器确认
const onDateConfirm = (value) => {
  const field = currentDateField.value;
  const dateType = field.props?.dateType || "date";

  // 使用工具方法格式化日期
  formData[field.prop] = formatDateByType(value, dateType);
  showDatePicker.value = false;
};

// 时间选择器确认
const onTimeConfirm = (value) => {
  const field = currentTimeField.value;

  // 使用 moment.js 格式化时间
  const timeString = `${value[0]}:${value[1]}`;
  const momentTime = moment(timeString, "HH:mm");

  // 根据字段配置决定输出格式
  const timeFormat = field.props?.timeFormat || "HH:mm";
  formData[field.prop] = momentTime.format(timeFormat);

  showTimePicker.value = false;
};

// 文件大小超限处理
const handleOversize = () => {
  showToast("文件大小超出限制");
};

// 格式化表单数据中的日期字段
const formatFormData = (data) => {
  const formattedData = { ...data };

  props.formConfig.forEach((field) => {
    if (
      (field.type === "date" || field.type === "time") &&
      formattedData[field.prop]
    ) {
      const dateType = field.props?.dateType || field.type;
      const timeFormat = field.props?.timeFormat || "HH:mm";

      if (field.type === "date") {
        formattedData[field.prop] = formatDateByType(
          formattedData[field.prop],
          dateType
        );
      } else if (field.type === "time") {
        const momentTime = moment(formattedData[field.prop], [
          "HH:mm",
          "HH:mm:ss",
        ]);
        if (momentTime.isValid()) {
          formattedData[field.prop] = momentTime.format(timeFormat);
        }
      }
    }
  });

  return formattedData;
};

// 表单提交
const handleSubmit = (values) => {
  // 格式化日期时间数据后再提交
  // const formattedValues = formatFormData(values)
  emit("update:modelValue", formData);
  emit("submit", formData);
};

// 表单验证失败
const handleFailed = (errorInfo) => {
  emit("failed", errorInfo);
};

const validate = () => {
  return formRef.value?.validate().then((res) => {
    return formData;
  });
};

const reset = () => {
  initFormData();
};

// 暴露方法
defineExpose({
  validate: validate,
  submit: () => formRef.value?.submit(),
  resetValidation: () => formRef.value?.resetValidation(),
  getValues: () => formatFormData(formData), // 获取值时也进行格式化
  setValues: (values) => {
    Object.assign(formData, values);
    // 设置默认值
    props.formConfig.forEach((field) => {
      // 设置选择器显示文本
      if (field.type === "select" && field.options) {
        const option = field.options.find(
          (opt) => opt.value === formData[field.prop]
        );
        selectTexts[field.prop] = option ? option.text : "";
      }
    });
  },
  getRawValues: () => ({ ...formData }), // 获取原始值（不格式化）
  // 新增：格式化指定日期字段
  formatDate: (date, format = "YYYY-MM-DD") => {
    return moment(date).format(format);
  },
  // 新增：解析日期字符串
  parseDate: (dateString) => {
    return moment(dateString).toDate();
  },
  reset,
});
</script>

<style lang="scss" scoped>
.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>
