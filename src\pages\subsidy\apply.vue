<!-- 补贴申请页面 -->
<template>
  <div class="subsidy-apply-page">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <van-steps :active="currentStep" direction="horizontal">
        <van-step>选择合同</van-step>
        <van-step>确认信息</van-step>
        <van-step>填写收款账户</van-step>
      </van-steps>
    </div>

    <!-- 第一步：选择合同 -->
    <div v-if="currentStep === 0" class="step-content">
      <FuniList
        ref="contractListRef"
        :tabs="contractTabs"
        :api-config="contractApiConfig"
        @item-click="handleContractSelect"
      >
        <template #item="{ item }">
          <div class="contract-item">
            <div class="contract-header">
              <div class="contract-status">
                <van-tag v-if="item.isReported" type="primary" size="mini">已备案</van-tag>
              </div>
              <div class="contract-title">{{ item.projectName }}</div>
              <van-radio
                :checked="selectedContract?.contractNumber === item.contractNumber"
                @click.stop="handleContractSelect(item)"
              />
            </div>
            <div class="contract-info">
              <div class="info-row">
                <span class="label">合同编号</span>
                <span class="value">{{ item.contractNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">合同类型</span>
                <span class="value">{{ item.contractType }}</span>
              </div>
              <div class="info-row">
                <span class="label">房源区域</span>
                <span class="value">{{ item.district }}</span>
              </div>
              <div class="info-row">
                <span class="label">房屋唯一号</span>
                <span class="value">{{ item.houseNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">建筑面积</span>
                <span class="value">{{ item.buildingArea }}㎡</span>
              </div>
            </div>
            <div v-if="item.isNotReported" class="not-reported-tag">
              <van-tag type="danger" size="mini">不满足网签</van-tag>
            </div>
          </div>
        </template>
      </FuniList>

      <!-- 提示信息 -->
      <div class="tip-section">
        <div class="tip-title">以下合同不满足网签补贴要求:</div>
        <FuniList
          ref="unqualifiedListRef"
          :tabs="unqualifiedTabs"
          :api-config="unqualifiedApiConfig"
        >
          <template #item="{ item }">
            <div class="contract-item unqualified">
              <div class="contract-header">
                <div class="contract-title">{{ item.projectName }}</div>
              </div>
              <div class="contract-info">
                <div class="info-row">
                  <span class="label">合同编号</span>
                  <span class="value">{{ item.contractNumber }}</span>
                </div>
                <div class="info-row">
                  <span class="label">合同类型</span>
                  <span class="value">{{ item.contractType }}</span>
                </div>
                <div class="info-row">
                  <span class="label">房源区域</span>
                  <span class="value">{{ item.district }}</span>
                </div>
                <div class="info-row">
                  <span class="label">房屋唯一号</span>
                  <span class="value">{{ item.houseNumber }}</span>
                </div>
                <div class="info-row">
                  <span class="label">建筑面积</span>
                  <span class="value">{{ item.buildingArea }}㎡</span>
                </div>
              </div>
              <div class="not-reported-tag">
                <van-tag type="danger" size="mini">已申报</van-tag>
              </div>
            </div>
          </template>
        </FuniList>
      </div>
    </div>

    <!-- 第二步：确认信息 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="info-section">
        <div class="section-title">房屋交易信息</div>
        <div class="info-card">
          <div class="info-row">
            <span class="label">购房人姓名</span>
            <span class="value">{{ contractDetail.buyerName || '**民' }}</span>
            <van-icon name="eye-o" class="mask-icon" />
          </div>
          <div class="info-row">
            <span class="label">购房人身份证号码</span>
            <span class="value">{{ contractDetail.buyerIdCard || '3345**************5555' }}</span>
            <van-icon name="eye-o" class="mask-icon" />
          </div>
          <div class="info-row">
            <span class="label">合同编号</span>
            <span class="value">{{ contractDetail.contractNumber || 'FY23456784567' }}</span>
          </div>
          <div class="info-row">
            <span class="label">网签日期</span>
            <span class="value">{{ contractDetail.signDate || '2024.03.04 12:00:00' }}</span>
          </div>
          <div class="info-row">
            <span class="label">房屋坐落</span>
            <span class="value">{{ contractDetail.houseLocation || '颍州区中心街2号祥生云境小区GC1-住宅楼2204室' }}</span>
          </div>
          <div class="info-row">
            <span class="label">合同金额（元）</span>
            <span class="value">{{ contractDetail.contractAmount || '1098765.00' }}</span>
          </div>
        </div>
      </div>

      <div class="info-section">
        <div class="section-title">不动产信息</div>
        <div class="info-card">
          <div class="info-row">
            <span class="label">房屋坐落</span>
            <span class="value">{{ contractDetail.propertyLocation || '颍州区中心街2号祥生云境小区GC1-住宅楼2204室' }}</span>
          </div>
          <div class="info-row">
            <span class="label">产权证</span>
            <span class="value">{{ contractDetail.propertyNumber || '皖（2025）阜阳市不动产权第8268288号' }}</span>
          </div>
          <div class="info-row">
            <span class="label">登记时间</span>
            <span class="value">{{ contractDetail.registerDate || '2024.03.04 12:00:00' }}</span>
          </div>
          <div class="info-row">
            <span class="label">不动产单元号</span>
            <span class="value">{{ contractDetail.propertyUnitNumber || '341202003004GB00051F0003' }}</span>
          </div>
        </div>
      </div>

      <div class="info-section">
        <div class="section-title">契税信息</div>
        <div class="info-card">
          <div class="info-row">
            <span class="label">契税登记号</span>
            <span class="value">{{ contractDetail.taxRegistrationNumber || '201134010003277202' }}</span>
          </div>
          <div class="info-row">
            <span class="label">实缴金额（元）</span>
            <span class="value">{{ contractDetail.taxAmount || '4569.09' }}</span>
          </div>
          <div class="info-row">
            <span class="label">开具时间</span>
            <span class="value">{{ contractDetail.taxIssueDate || '2024.03.04 12:00:00' }}</span>
          </div>
          <div class="info-row">
            <span class="label">契税对应房产信息</span>
            <span class="value">{{ contractDetail.taxPropertyInfo || '房源编号:F341202202500240045 房屋坐落位置:祥生云境小区GC1-住宅楼2204室 房屋面积:124.45平米 合同签订时间:2023-03-15' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三步：填写收款账户 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="account-info-section">
        <div class="account-tip">
          （颍州、颍泉区住房建设中国农业银行有限公司，颍泉区住房建设当地农村商业银行开户，刘XX银行XX支行）
        </div>

        <FuniForm
          ref="accountFormRef"
          :form-config="accountFormConfig"
          v-model="accountFormData"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button
        v-if="currentStep > 0"
        plain
        type="primary"
        @click="handlePrevStep"
        class="action-btn"
      >
        上一步
      </van-button>
      <van-button
        type="primary"
        @click="handleNextStep"
        class="action-btn"
        :loading="submitting"
      >
        {{ currentStep === 2 ? '提交' : '下一步' }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showSuccessToast, showFailToast, showToast } from 'vant'
import { subsidyApi } from '@/api/subsidy'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const submitting = ref(false)
const selectedContract = ref(null)
const contractDetail = ref({})
const accountFormData = ref({})
const accountFormRef = ref()

// 合同列表配置
const contractTabs = [
  { key: 'qualified', title: '符合条件的合同', index: 0 }
]

const contractApiConfig = {
  qualified: {
    apiFunction: subsidyApi.getQualifiedContracts
  }
}

// 不符合条件的合同列表配置
const unqualifiedTabs = [
  { key: 'unqualified', title: '不符合条件', index: 0 }
]

const unqualifiedApiConfig = {
  unqualified: {
    apiFunction: subsidyApi.getUnqualifiedContracts
  }
}

// 收款账户表单配置
const accountFormConfig = [
  {
    prop: 'accountName',
    label: '开户人姓名',
    type: 'input',
    rules: [{ required: true, message: '请输入开户人姓名' }],
    props: {
      placeholder: '请输入',
      readonly: true
    }
  },
  {
    prop: 'accountIdCard',
    label: '开户人身份证',
    type: 'input',
    rules: [{ required: true, message: '请输入开户人身份证' }],
    props: {
      placeholder: '请输入'
    }
  },
  {
    prop: 'bankAccount',
    label: '银行账号',
    type: 'input',
    rules: [{ required: true, message: '请输入银行账号' }],
    props: {
      placeholder: '请输入'
    }
  },
  {
    prop: 'bankName',
    label: '银行名称',
    type: 'input',
    rules: [{ required: true, message: '请输入银行名称' }],
    props: {
      placeholder: '请输入'
    }
  }
]

// 选择合同
const handleContractSelect = (contract) => {
  selectedContract.value = contract
}

// 上一步
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 下一步
const handleNextStep = async () => {
  if (currentStep.value === 0) {
    // 第一步：验证是否选择了合同
    if (!selectedContract.value) {
      showToast('请选择一个合同')
      return
    }
    // 获取合同详情
    await fetchContractDetail()
    currentStep.value++
  } else if (currentStep.value === 1) {
    // 第二步：直接进入下一步
    currentStep.value++
    // 初始化账户信息
    initAccountForm()
  } else if (currentStep.value === 2) {
    // 第三步：提交申请
    await handleSubmit()
  }
}

// 获取合同详情
const fetchContractDetail = async () => {
  try {
    const response = await subsidyApi.getContractInfo(selectedContract.value.contractNumber)
    contractDetail.value = response
  } catch (error) {
    showFailToast('获取合同详情失败')
  }
}

// 初始化账户表单
const initAccountForm = () => {
  accountFormData.value = {
    accountName: contractDetail.value.buyerName || '**民',
    accountIdCard: contractDetail.value.buyerIdCard || '3345**************5555',
    bankAccount: '',
    bankName: ''
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    // 验证表单
    await accountFormRef.value?.validate()

    submitting.value = true

    const submitData = {
      contractNumber: selectedContract.value.contractNumber,
      contractDetail: contractDetail.value,
      accountInfo: accountFormData.value
    }

    await subsidyApi.submitApplication(submitData)
    showSuccessToast('申请提交成功')
    router.push('/progress')
  } catch (error) {
    showFailToast('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.subsidy-apply-page {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.steps-container {
  background: white;
  padding: 20px 16px;
  margin-bottom: 8px;
}

.step-content {
  padding: 16px;
}

.contract-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;

  .contract-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;

    .contract-status {
      margin-bottom: 8px;
    }

    .contract-title {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: var(--van-text-color);
      margin-right: 12px;
    }
  }

  .contract-info {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--van-border-color);

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: var(--van-text-color-2);
        font-size: 14px;
        width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: var(--van-text-color);
        font-size: 14px;
        text-align: right;
        flex: 1;
      }
    }
  }

  .not-reported-tag {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  &.unqualified {
    opacity: 0.6;
  }
}

.tip-section {
  margin-top: 24px;

  .tip-title {
    font-size: 14px;
    color: var(--van-text-color-2);
    margin-bottom: 12px;
    padding: 0 4px;
  }
}

.info-section {
  margin-bottom: 24px;

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--van-text-color);
    margin-bottom: 12px;
  }

  .info-card {
    background: white;
    border-radius: 8px;
    padding: 16px;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--van-border-color);
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: var(--van-text-color-2);
        font-size: 14px;
        width: 120px;
        flex-shrink: 0;
      }

      .value {
        color: var(--van-text-color);
        font-size: 14px;
        text-align: right;
        flex: 1;
        word-break: break-all;
      }

      .mask-icon {
        color: var(--van-primary-color);
        font-size: 16px;
        margin-left: 8px;
      }
    }
  }
}

.account-info-section {
  .account-tip {
    background: #fff7e6;
    color: #ff8f00;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 16px;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--van-border-color);
  z-index: 100;

  .action-btn {
    flex: 1;
    height: 44px;
    border-radius: 22px;
  }
}

// 为底部按钮预留空间
.step-content {
  padding-bottom: 80px;
}
</style>

<route lang="json5">
{
  name: 'SubsidyApply',
  meta: {
    title: '补贴申请'
  }
}
</route>
