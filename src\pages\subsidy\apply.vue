<!-- 补贴申请页面 -->
<template>
  <div class="subsidy-apply-page">
    <div class="page-content">
      <van-notice-bar
        left-icon="info-o"
        text="请确保填写信息真实有效，虚假信息将影响申请结果"
        background="#fff7e6"
        color="#ff8f00"
      />

      <van-form @submit="handleSubmit" @failed="handleFailed">
        <!-- 基本信息 -->
        <van-cell-group title="基本信息" inset>
          <van-field
            v-model="formData.name"
            name="name"
            label="姓名"
            placeholder="请输入姓名"
            :rules="[{ required: true, message: '请输入姓名' }]"
          />
          <van-field
            v-model="formData.phone"
            name="phone"
            label="手机号"
            placeholder="请输入手机号"
            :rules="[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
            ]"
          />
          <van-field
            v-model="formData.idCard"
            name="idCard"
            label="身份证号"
            placeholder="请输入身份证号"
            :rules="[{ required: true, message: '请输入身份证号' }]"
          />
        </van-cell-group>

        <!-- 购房信息 -->
        <van-cell-group title="购房信息" inset>
          <van-field
            v-model="formData.contractNumber"
            name="contractNumber"
            label="合同编号"
            placeholder="请输入购房合同编号"
            :rules="[{ required: true, message: '请输入购房合同编号' }]"
          />
          <van-field
            v-model="formData.houseAddress"
            name="houseAddress"
            label="房屋地址"
            placeholder="请输入房屋地址"
            :rules="[{ required: true, message: '请输入房屋地址' }]"
          />
          <van-field
            v-model="formData.houseArea"
            name="houseArea"
            label="建筑面积"
            placeholder="请输入建筑面积"
            suffix="㎡"
            type="number"
            :rules="[{ required: true, message: '请输入建筑面积' }]"
          />
          <van-field
            v-model="formData.housePrice"
            name="housePrice"
            label="购房总价"
            placeholder="请输入购房总价"
            suffix="元"
            type="number"
            :rules="[{ required: true, message: '请输入购房总价' }]"
          />
          <van-field
            v-model="formData.purchaseDate"
            name="purchaseDate"
            label="购房日期"
            placeholder="请选择购房日期"
            readonly
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择购房日期' }]"
          />
        </van-cell-group>

        <!-- 收款账户 -->
        <van-cell-group title="收款账户" inset>
          <van-field
            v-model="formData.bankName"
            name="bankName"
            label="开户银行"
            placeholder="请输入开户银行"
            :rules="[{ required: true, message: '请输入开户银行' }]"
          />
          <van-field
            v-model="formData.accountNumber"
            name="accountNumber"
            label="银行账号"
            placeholder="请输入银行账号"
            :rules="[{ required: true, message: '请输入银行账号' }]"
          />
          <van-field
            v-model="formData.accountName"
            name="accountName"
            label="账户姓名"
            placeholder="请输入账户姓名"
            :rules="[{ required: true, message: '请输入账户姓名' }]"
          />
        </van-cell-group>

        <!-- 材料上传 -->
        <van-cell-group title="材料上传" inset>
          <van-cell title="身份证" is-link @click="uploadMaterial('idCard')">
            <template #value>
              <span :class="{ 'uploaded': materials.idCard }">
                {{ materials.idCard ? '已上传' : '未上传' }}
              </span>
            </template>
          </van-cell>
          <van-cell title="购房合同" is-link @click="uploadMaterial('contract')">
            <template #value>
              <span :class="{ 'uploaded': materials.contract }">
                {{ materials.contract ? '已上传' : '未上传' }}
              </span>
            </template>
          </van-cell>
          <van-cell title="房产证" is-link @click="uploadMaterial('houseCert')">
            <template #value>
              <span :class="{ 'uploaded': materials.houseCert }">
                {{ materials.houseCert ? '已上传' : '未上传' }}
              </span>
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            native-type="submit"
            block
            round
            :loading="submitting"
          >
            提交申请
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="selectedDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :max-date="new Date()"
        title="选择购房日期"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { subsidyApi } from '@/api/subsidy'

const router = useRouter()

// 响应式数据
const submitting = ref(false)
const showDatePicker = ref(false)
const selectedDate = ref(new Date())

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  idCard: '',
  contractNumber: '',
  houseAddress: '',
  houseArea: '',
  housePrice: '',
  purchaseDate: '',
  bankName: '',
  accountNumber: '',
  accountName: ''
})

// 材料上传状态
const materials = reactive({
  idCard: false,
  contract: false,
  houseCert: false
})

// 日期确认
const onDateConfirm = (date) => {
  formData.purchaseDate = date.toISOString().split('T')[0]
  showDatePicker.value = false
}

// 上传材料
const uploadMaterial = (type) => {
  // 这里应该调用文件上传组件
  showSuccessToast('上传功能开发中')
  materials[type] = true
}

// 表单提交
const handleSubmit = async (values) => {
  submitting.value = true
  try {
    await subsidyApi.submitApplication({
      ...values,
      materials: materials
    })
    showSuccessToast('申请提交成功')
    router.push('/progress')
  } catch (error) {
    showFailToast('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 表单验证失败
const handleFailed = (errorInfo) => {
  showFailToast('请完善必填信息')
}
</script>

<style lang="scss" scoped>
.subsidy-apply-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.page-content {
  padding: 16px;
}

.van-cell-group {
  margin-bottom: 16px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

.uploaded {
  color: var(--van-success-color);
}
</style>

<route lang="json5">
{
  name: 'SubsidyApply',
  meta: {
    title: '补贴申请'
  }
}
</route>
