/**
 * 补贴申请相关API接口
 */

export const subsidyApi = {
  /**
   * 根据合同编号获取合同信息
   * @param {string} contractNumber - 合同编号
   * @returns {Promise} 返回合同信息
   */
  getContractInfo: (contractNumber) => {
    return window.$http.fetch(`/api/subsidy/contract/${contractNumber}`)
  },

  /**
   * 提交补贴申请
   * @param {Object} data - 申请数据
   * @param {string} data.contractNumber - 合同编号
   * @param {string} data.contractDate - 签约日期
   * @param {Object} data.accountInfo - 收款账户信息
   * @returns {Promise} 返回申请结果
   */
  submitApplication: (data) => {
    return window.$http.post('/api/subsidy/applications', data)
  },

  /**
   * 获取申请列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.status] - 申请状态
   * @returns {Promise} 返回申请列表
   */
  getApplicationList: (params) => {
    return window.$http.fetch('/api/subsidy/applications', params)
  },

  /**
   * 获取申请详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回申请详情
   */
  getApplicationDetail: (id) => {
    return window.$http.fetch(`/api/subsidy/applications/${id}`)
  },

  /**
   * 撤销申请
   * @param {string} id - 申请ID
   * @returns {Promise} 返回操作结果
   */
  cancelApplication: (id) => {
    return window.$http.put(`/api/subsidy/applications/${id}/cancel`)
  },

  /**
   * 修改申请
   * @param {string} id - 申请ID
   * @param {Object} data - 修改数据
   * @returns {Promise} 返回操作结果
   */
  modifyApplication: (id, data) => {
    return window.$http.put(`/api/subsidy/applications/${id}`, data)
  }
}
