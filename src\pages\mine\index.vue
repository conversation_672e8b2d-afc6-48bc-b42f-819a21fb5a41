<!-- 个人中心页面 -->
<template>
  <div class="profile-page">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-info">
        <van-image
          class="avatar"
          :src="userInfo.avatar || '/images/default-avatar.png'"
          round
          width="60"
          height="60"
        />
        <div class="user-details">
          <h3 class="username">{{ userInfo.name || '未登录' }}</h3>
          <p class="user-desc">{{ userInfo.phone || '请先登录' }}</p>
        </div>
      </div>
      <van-icon name="arrow" class="user-arrow" @click="navigateToUserInfo" />
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stats-item" @click="navigateTo('/progress')">
          <div class="stats-number">{{ userStats.totalApplications }}</div>
          <div class="stats-label">我的申请</div>
        </div>
        <div class="stats-item" @click="navigateTo('/progress?status=approved')">
          <div class="stats-number">{{ userStats.approvedApplications }}</div>
          <div class="stats-label">已通过</div>
        </div>
        <div class="stats-item" @click="navigateTo('/progress?status=pending')">
          <div class="stats-number">{{ userStats.pendingApplications }}</div>
          <div class="stats-label">审核中</div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="我的收藏"
          icon="star-o"
          is-link
          @click="navigateTo('/profile/favorites')"
        />
        <van-cell
          title="申请记录"
          icon="orders-o"
          is-link
          @click="navigateTo('/progress')"
        />
        <van-cell
          title="消息通知"
          icon="bell"
          is-link
          @click="navigateTo('/profile/notifications')"
        >
          <template #value>
            <van-badge v-if="unreadCount > 0" :content="unreadCount" />
          </template>
        </van-cell>
        <van-cell
          title="帮助中心"
          icon="question-o"
          is-link
          @click="navigateTo('/profile/help')"
        />
        <van-cell
          title="意见反馈"
          icon="comment-o"
          is-link
          @click="navigateTo('/profile/feedback')"
        />
        <van-cell
          title="关于我们"
          icon="info-o"
          is-link
          @click="navigateTo('/profile/about')"
        />
      </van-cell-group>
    </div>

    <!-- 设置菜单 -->
    <div class="settings-section">
      <van-cell-group>
        <van-cell
          title="账号设置"
          icon="setting-o"
          is-link
          @click="navigateTo('/profile/settings')"
        />
        <van-cell
          title="隐私政策"
          icon="shield-o"
          is-link
          @click="navigateTo('/profile/privacy')"
        />
        <van-cell
          title="服务条款"
          icon="description"
          is-link
          @click="navigateTo('/profile/terms')"
        />
      </van-cell-group>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section" v-if="userInfo.isLoggedIn">
      <van-button
        type="danger"
        block
        round
        @click="handleLogout"
      >
        退出登录
      </van-button>
    </div>

    <!-- 登录按钮 -->
    <div class="login-section" v-else>
      <van-button
        type="primary"
        block
        round
        @click="handleLogin"
      >
        立即登录
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast } from 'vant'

const router = useRouter()

// 响应式数据
const userInfo = reactive({
  name: '张三',
  phone: '138****8888',
  avatar: '',
  isLoggedIn: true
})

const userStats = reactive({
  totalApplications: 3,
  approvedApplications: 1,
  pendingApplications: 2
})

const unreadCount = ref(2)

// 导航方法
const navigateTo = (path) => {
  router.push(path)
}

// 导航到用户信息页
const navigateToUserInfo = () => {
  if (userInfo.isLoggedIn) {
    navigateTo('/profile/user-info')
  } else {
    handleLogin()
  }
}

// 处理登录
const handleLogin = () => {
  // 这里应该跳转到登录页面或调用登录接口
  navigateTo('/login')
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })
    
    // 清除用户信息
    userInfo.name = ''
    userInfo.phone = ''
    userInfo.avatar = ''
    userInfo.isLoggedIn = false
    
    // 重置统计信息
    userStats.totalApplications = 0
    userStats.approvedApplications = 0
    userStats.pendingApplications = 0
    
    showSuccessToast('已退出登录')
  } catch (error) {
    // 用户取消退出
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 这里应该调用获取用户信息的API
    // const response = await userApi.getUserInfo()
    // userInfo.value = response
    
    // Mock数据
    if (userInfo.isLoggedIn) {
      // 获取用户统计信息
      // const statsResponse = await userApi.getUserStats()
      // userStats.value = statsResponse
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})

onActivated(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

// 用户信息卡片
.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  margin-right: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.user-desc {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

.user-arrow {
  color: white;
  font-size: 16px;
  opacity: 0.8;
}

// 统计信息
.stats-section {
  background: white;
  margin: 16px;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stats-item {
  text-align: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.stats-item:active {
  background-color: var(--van-active-color);
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  color: var(--van-primary-color);
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: var(--van-text-color-2);
}

// 功能菜单
.menu-section {
  margin: 16px;
}

.settings-section {
  margin: 16px;
}

// 退出登录按钮
.logout-section {
  margin: 32px 16px 16px 16px;
}

.login-section {
  margin: 32px 16px 16px 16px;
}

// 自定义cell样式
:deep(.van-cell) {
  padding: 16px;
  
  .van-cell__title {
    font-size: 15px;
  }
  
  .van-cell__left-icon {
    margin-right: 12px;
    font-size: 18px;
    color: var(--van-primary-color);
  }
}

:deep(.van-cell-group) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
</style>

<route lang="json5">
{
  name: 'Mine',
  meta: {
    title: '我的'
  }
}
</route>
