<!-- 进度查询页面 -->
<template>
  <div class="progress-page">
    <!-- 状态筛选 -->
    <div class="filter-section">
      <van-tabs v-model:active="activeStatus" @change="onStatusChange">
        <van-tab title="全部" name="all" />
        <van-tab title="审核中" name="pending" />
        <van-tab title="已通过" name="approved" />
        <van-tab title="已拒绝" name="rejected" />
      </van-tabs>
    </div>

    <!-- 申请列表 -->
    <div class="application-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="application in applicationList"
            :key="application.id"
            class="application-item"
            @click="navigateToDetail(application.id)"
          >
            <div class="application-header">
              <h3 class="application-title">{{ application.title }}</h3>
              <van-tag 
                :type="getStatusType(application.status)" 
                size="mini"
              >
                {{ application.statusText }}
              </van-tag>
            </div>
            <div class="application-content">
              <div class="info-row">
                <span class="label">申请金额：</span>
                <span class="value amount">{{ formatAmount(application.amount) }}</span>
              </div>
              <div class="info-row">
                <span class="label">提交时间：</span>
                <span class="value">{{ application.submitTime }}</span>
              </div>
              <div class="info-row" v-if="application.processTime">
                <span class="label">处理时间：</span>
                <span class="value">{{ application.processTime }}</span>
              </div>
            </div>
            <div class="application-footer">
              <div class="progress-info">
                <van-progress 
                  :percentage="getProgressPercentage(application.status)" 
                  :color="getProgressColor(application.status)"
                  stroke-width="4"
                />
                <span class="progress-text">{{ getProgressText(application.status) }}</span>
              </div>
              <van-icon name="arrow" class="application-arrow" />
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && applicationList.length === 0"
      description="暂无申请记录"
      image="search"
    >
      <van-button 
        type="primary" 
        size="small" 
        @click="navigateToApply"
      >
        立即申请
      </van-button>
    </van-empty>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { progressApi } from '@/api/progress'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const activeStatus = ref('all')
const applicationList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 导航到详情页
const navigateToDetail = (id) => {
  router.push(`/progress/detail/${id}`)
}

// 导航到申请页
const navigateToApply = () => {
  router.push('/subsidy/apply')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'processing': 'primary'
  }
  return statusMap[status] || 'default'
}

// 获取进度百分比
const getProgressPercentage = (status) => {
  const progressMap = {
    'pending': 30,
    'processing': 60,
    'approved': 100,
    'rejected': 100
  }
  return progressMap[status] || 0
}

// 获取进度颜色
const getProgressColor = (status) => {
  const colorMap = {
    'pending': '#ff8f00',
    'processing': '#1989fa',
    'approved': '#07c160',
    'rejected': '#ee0a24'
  }
  return colorMap[status] || '#dcdee0'
}

// 获取进度文本
const getProgressText = (status) => {
  const textMap = {
    'pending': '材料审核中',
    'processing': '审批处理中',
    'approved': '审批完成',
    'rejected': '审批未通过'
  }
  return textMap[status] || '未知状态'
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万元'
  }
  return amount + '元'
}

// 状态切换
const onStatusChange = () => {
  resetList()
  loadApplicationList()
}

// 重置列表
const resetList = () => {
  applicationList.value = []
  currentPage.value = 1
  finished.value = false
}

// 下拉刷新
const onRefresh = () => {
  resetList()
  loadApplicationList().finally(() => {
    refreshing.value = false
  })
}

// 上拉加载
const onLoad = () => {
  loadApplicationList()
}

// 加载申请列表
const loadApplicationList = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      status: activeStatus.value === 'all' ? '' : activeStatus.value
    }
    
    // Mock数据
    const mockData = {
      list: [
        {
          id: '1',
          title: '首次购房补贴申请',
          status: 'approved',
          statusText: '已通过',
          amount: 50000,
          submitTime: '2024-01-15',
          processTime: '2024-01-20',
          description: '首次在本市购买商品住房补贴申请'
        },
        {
          id: '2',
          title: '人才购房补贴申请',
          status: 'processing',
          statusText: '审批中',
          amount: 80000,
          submitTime: '2024-01-18',
          processTime: null,
          description: '高层次人才购房补贴申请'
        },
        {
          id: '3',
          title: '青年人才购房补贴申请',
          status: 'pending',
          statusText: '审核中',
          amount: 30000,
          submitTime: '2024-01-20',
          processTime: null,
          description: '35岁以下青年人才购房补贴申请'
        }
      ],
      total: 3
    }
    
    // 根据状态筛选
    let filteredList = mockData.list
    if (activeStatus.value !== 'all') {
      filteredList = mockData.list.filter(item => item.status === activeStatus.value)
    }
    
    if (currentPage.value === 1) {
      applicationList.value = filteredList || []
    } else {
      applicationList.value.push(...(filteredList || []))
    }
    
    // 判断是否还有更多数据
    if (!filteredList || filteredList.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载申请列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadApplicationList()
})

onActivated(() => {
  // 页面激活时刷新数据
  loadApplicationList()
})
</script>

<style lang="scss" scoped>
.progress-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.filter-section {
  background: white;
  border-bottom: 1px solid var(--van-border-color);
}

.application-list {
  padding: 16px;
}

.application-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
}

.application-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.application-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin: 0;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.application-content {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 14px;
  color: var(--van-text-color-2);
  width: 80px;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: var(--van-text-color);
  flex: 1;
}

.value.amount {
  color: var(--van-primary-color);
  font-weight: 600;
}

.application-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid var(--van-border-color);
}

.progress-info {
  flex: 1;
  margin-right: 16px;
}

.progress-text {
  font-size: 12px;
  color: var(--van-text-color-2);
  margin-top: 4px;
  display: block;
}

.application-arrow {
  color: var(--van-text-color-3);
  font-size: 14px;
}
</style>

<route lang="json5">
{
  name: 'Progress',
  meta: {
    title: '进度查询'
  }
}
</route>
