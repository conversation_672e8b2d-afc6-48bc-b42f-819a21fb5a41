<!-- 购房补贴首页 -->
<template>
  <div class="home-page">
    <!-- 头部横幅区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">院企直达，惠企服务</h1>
        <p class="hero-subtitle">政策、兑现、服务一网通</p>
      </div>
      <div class="hero-decoration">
        <!-- 装饰性图形元素 -->
        <div class="decoration-shape decoration-shape-1"></div>
        <div class="decoration-shape decoration-shape-2"></div>
        <div class="decoration-shape decoration-shape-3"></div>
      </div>
    </div>

    <!-- 功能模块区域 -->
    <div class="modules-section">
      <div class="modules-grid">
        <!-- 补贴申请 -->
        <div class="module-item" @click="navigateTo('/subsidy/apply')">
          <div class="module-icon module-icon-subsidy">
            <van-icon name="gold-coin-o" />
          </div>
          <span class="module-title">补贴申请</span>
        </div>

        <!-- 热门政策 -->
        <div class="module-item" @click="navigateTo('/policy/list')">
          <div class="module-icon module-icon-policy">
            <van-icon name="fire-o" />
          </div>
          <span class="module-title">热门政策</span>
        </div>

        <!-- 奖补公示 -->
        <div class="module-item" @click="navigateTo('/award/list')">
          <div class="module-icon module-icon-award">
            <van-icon name="star-o" />
          </div>
          <span class="module-title">奖补公示</span>
        </div>

        <!-- 申报指南 -->
        <div class="module-item" @click="navigateTo('/guide/list')">
          <div class="module-icon module-icon-guide">
            <van-icon name="guide-o" />
          </div>
          <span class="module-title">申报指南</span>
        </div>
      </div>
    </div>

    <!-- 最新公告区域 -->
    <div class="notice-section" v-if="noticeList.length > 0">
      <div class="section-header">
        <h3 class="section-title">最新公告</h3>
        <span class="more-link" @click="navigateTo('/notice/list')">更多</span>
      </div>
      <div class="notice-list">
        <div 
          v-for="notice in noticeList.slice(0, 3)" 
          :key="notice.id"
          class="notice-item"
          @click="navigateTo(`/notice/detail/${notice.id}`)"
        >
          <div class="notice-content">
            <h4 class="notice-title">{{ notice.title }}</h4>
            <p class="notice-summary">{{ notice.summary }}</p>
            <span class="notice-time">{{ notice.publishTime }}</span>
          </div>
          <van-icon name="arrow" class="notice-arrow" />
        </div>
      </div>
    </div>

    <!-- 快速统计区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stats-item">
          <div class="stats-number">{{ stats.totalApplications }}</div>
          <div class="stats-label">累计申请</div>
        </div>
        <div class="stats-item">
          <div class="stats-number">{{ stats.approvedApplications }}</div>
          <div class="stats-label">已通过</div>
        </div>
        <div class="stats-item">
          <div class="stats-number">{{ formatAmount(stats.totalAmount) }}</div>
          <div class="stats-label">补贴总额</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { homeApi } from '@/api/home'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const noticeList = ref([])
const stats = ref({
  totalApplications: 0,
  approvedApplications: 0,
  totalAmount: 0
})

// 导航方法
const navigateTo = (path) => {
  router.push(path)
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toString()
}

// 获取首页数据
const fetchHomeData = async () => {
  loading.value = true
  try {
    // 获取公告列表
    const noticeResponse = await homeApi.getNoticeList({ current: 1, size: 3 })
    noticeList.value = noticeResponse.list || []

    // Mock统计数据
    stats.value = {
      totalApplications: 1256,
      approvedApplications: 1089,
      totalAmount: 58900000
    }
  } catch (error) {
    console.error('获取首页数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchHomeData()
})

onActivated(() => {
  fetchHomeData()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

// 头部横幅区域
.hero-section {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.hero-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

// 装饰性图形
.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.decoration-shape-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
}

.decoration-shape-2 {
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: -40px;
}

.decoration-shape-3 {
  width: 60px;
  height: 60px;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
}

// 功能模块区域
.modules-section {
  padding: 24px 16px;
  background: white;
  margin-top: -20px;
  border-radius: 20px 20px 0 0;
  position: relative;
  z-index: 3;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.module-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  background: var(--van-background);
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.module-item:active {
  transform: scale(0.98);
  background: var(--van-active-color);
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  font-size: 24px;
  color: white;
}

.module-icon-subsidy {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
}

.module-icon-policy {
  background: linear-gradient(135deg, #ffd93d 0%, #ff9800 100%);
}

.module-icon-award {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.module-icon-guide {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.module-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--van-text-color);
}

// 公告区域
.notice-section {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--van-text-color);
}

.more-link {
  font-size: 12px;
  color: var(--van-primary-color);
}

.notice-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--van-border-color);
  cursor: pointer;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-content {
  flex: 1;
}

.notice-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: var(--van-text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notice-summary {
  font-size: 12px;
  color: var(--van-text-color-2);
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notice-time {
  font-size: 11px;
  color: var(--van-text-color-3);
}

.notice-arrow {
  color: var(--van-text-color-3);
  font-size: 12px;
}

// 统计区域
.stats-section {
  margin: 16px;
  background: white;
  border-radius: 12px;
  padding: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  color: var(--van-primary-color);
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: var(--van-text-color-2);
}
</style>

<route lang="json5">
{
  name: 'Home',
  meta: {
    title: '购房补贴'
  }
}
</route>
