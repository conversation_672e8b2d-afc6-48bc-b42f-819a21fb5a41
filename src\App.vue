<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 底部导航配置
const tabbarActive = ref(0)
const tabbarList = [
  { name: 'home', label: '首页', icon: 'home-o' },
  { name: 'progress', label: '进度', icon: 'orders-o' },
  { name: 'profile', label: '我的', icon: 'user-o' }
]

// 监听路由变化更新tabbar状态
const updateTabbarActive = () => {
  const routeName = route.name
  const index = tabbarList.findIndex(item => item.name === routeName)
  if (index !== -1) {
    tabbarActive.value = index
  }
}

// 切换tabbar
const onTabbarChange = (index) => {
  const targetRoute = tabbarList[index].name
  if (route.name !== targetRoute) {
    router.push({ name: targetRoute })
  }
}

// 监听路由变化
watch(() => route.name, updateTabbarActive, { immediate: true })
</script>

<template>
  <van-config-provider>
    <nav-bar />
    <router-view v-slot="{ Component }">
      <section class="app-wrapper">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </section>
    </router-view>

    <!-- 底部导航栏 -->
    <van-tabbar
      v-model="tabbarActive"
      @change="onTabbarChange"
      fixed
      placeholder
    >
      <van-tabbar-item
        v-for="(item, index) in tabbarList"
        :key="index"
        :icon="item.icon"
      >
        {{ item.label }}
      </van-tabbar-item>
    </van-tabbar>
  </van-config-provider>
</template>

<style scoped>
.app-wrapper {
  width: 100%;
  position: relative;
  padding-bottom: 50px; /* 为底部导航栏预留空间 */
}
</style>

