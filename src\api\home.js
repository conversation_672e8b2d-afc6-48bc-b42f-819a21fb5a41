/**
 * 首页相关API接口
 */

export const homeApi = {
  /**
   * 获取首页轮播图
   * @returns {Promise} 返回轮播图列表
   */
  getBannerList: () => {
    return window.$http.fetch('/api/home/<USER>')
  },

  /**
   * 获取最新公告列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回公告列表数据
   */
  getNoticeList: (params) => {
    return window.$http.fetch('/api/home/<USER>', params)
  },

  /**
   * 获取公告详情
   * @param {string} id - 公告ID
   * @returns {Promise} 返回公告详情
   */
  getNoticeDetail: (id) => {
    return window.$http.fetch(`/api/home/<USER>/${id}`)
  }
}
