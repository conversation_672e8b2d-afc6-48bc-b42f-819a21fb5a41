/**
 * 进度查询相关API接口
 */

export const progressApi = {
  /**
   * 获取申请进度列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.status] - 申请状态
   * @param {string} [params.type] - 申请类型
   * @returns {Promise} 返回进度列表
   */
  getProgressList: (params) => {
    return window.$http.fetch('/api/progress/applications', params)
  },

  /**
   * 获取申请进度详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回进度详情
   */
  getProgressDetail: (id) => {
    return window.$http.fetch(`/api/progress/applications/${id}`)
  },

  /**
   * 获取申请办理步骤
   * @param {string} id - 申请ID
   * @returns {Promise} 返回办理步骤列表
   */
  getProgressSteps: (id) => {
    return window.$http.fetch(`/api/progress/applications/${id}/steps`)
  },

  /**
   * 预览申请材料
   * @param {string} applicationId - 申请ID
   * @param {string} materialId - 材料ID
   * @returns {Promise} 返回材料预览链接
   */
  previewMaterial: (applicationId, materialId) => {
    return window.$http.fetch(`/api/progress/applications/${applicationId}/materials/${materialId}/preview`)
  },

  /**
   * 下载申请材料
   * @param {string} applicationId - 申请ID
   * @param {string} materialId - 材料ID
   * @returns {Promise} 返回材料下载链接
   */
  downloadMaterial: (applicationId, materialId) => {
    return window.$http.fetch(`/api/progress/applications/${applicationId}/materials/${materialId}/download`)
  }
}
